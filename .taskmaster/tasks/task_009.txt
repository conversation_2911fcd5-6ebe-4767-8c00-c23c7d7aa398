# Task ID: 9
# Title: Build Custom Prompt Editor Interface
# Status: pending
# Dependencies: 8
# Priority: high
# Description: Create dual-editor interface for Fact Extraction and Memory Evolution prompts with validation and syntax highlighting
# Details:
Implement PromptEditor component using Monaco Editor or CodeMirror with syntax highlighting. Create side-by-side layout for both prompt types. Add 4000 character limit with real-time counter. Implement auto-save every 30 seconds. Add validation for prompt structure and required sections. Include version history with diff view. Add import/export functionality and template library.

# Test Strategy:
Test editor functionality, validation rules, auto-save, character limits, version history, and import/export features.
